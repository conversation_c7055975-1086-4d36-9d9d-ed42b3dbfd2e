/*
 * -------------------------------------------------------
 * Section: Storage
 * We create the schema for the storage
 * -------------------------------------------------------
 */

-- Account Image
insert into
  storage.buckets (id, name, PUBLIC)
values
  ('account_image', 'account_image', true);

-- Generated Images (AI-generated and reference images)
insert into
  storage.buckets (id, name, PUBLIC, file_size_limit, allowed_mime_types)
values
  ('generated', 'generated', true, ********, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']);

-- Function: get the storage filename as a UUID.
-- Useful if you want to name files with UUIDs related to an account
create
or replace function kit.get_storage_filename_as_uuid (name text) returns uuid
set
  search_path = '' as $$
begin
    return replace(storage.filename(name), concat('.',
	storage.extension(name)), '')::uuid;

end;

$$ language plpgsql;

grant
execute on function kit.get_storage_filename_as_uuid (text) to authenticated,
service_role;

-- RLS policies for storage bucket account_image
create policy account_image on storage.objects for all using (
  bucket_id = 'account_image'
  and (
    kit.get_storage_filename_as_uuid(name) = auth.uid()
    or public.has_role_on_account(kit.get_storage_filename_as_uuid(name))
  )
)
with check (
  bucket_id = 'account_image'
  and (
    kit.get_storage_filename_as_uuid(name) = auth.uid()
    or public.has_permission(
      auth.uid(),
      kit.get_storage_filename_as_uuid(name),
      'settings.manage'
    )
  )
);

-- RLS policies for storage bucket generated
-- Users can upload images for accounts they have access to
create policy "Users can upload images for their accounts" on storage.objects for insert with check (
  bucket_id = 'generated'
  and (
    storage.foldername(name))[1] in (
      select id::text from public.accounts
      where id in (
        select account_id from public.accounts_memberships
        where user_id = auth.uid()
      )
    )
);

-- Users can view images for accounts they have access to
create policy "Users can view images for their accounts" on storage.objects for select using (
  bucket_id = 'generated'
  and (
    storage.foldername(name))[1] in (
      select id::text from public.accounts
      where id in (
        select account_id from public.accounts_memberships
        where user_id = auth.uid()
      )
    )
);

-- Users can update images for accounts they have access to
create policy "Users can update images for their accounts" on storage.objects for update using (
  bucket_id = 'generated'
  and (
    storage.foldername(name))[1] in (
      select id::text from public.accounts
      where id in (
        select account_id from public.accounts_memberships
        where user_id = auth.uid()
      )
    )
);

-- Users can delete images for accounts they have access to
create policy "Users can delete images for their accounts" on storage.objects for delete using (
  bucket_id = 'generated'
  and (
    storage.foldername(name))[1] in (
      select id::text from public.accounts
      where id in (
        select account_id from public.accounts_memberships
        where user_id = auth.uid()
      )
    )
);

-- Service role can do everything (for server-side uploads)
create policy "Service role can manage all generated images" on storage.objects for all using (
  bucket_id = 'generated'
) with check (
  bucket_id = 'generated'
);