-- Ensure UUID generator is available
create extension if not exists pgcrypto;

-- Add a primary key to public.config so Zero can sync it
alter table public.config add column if not exists id uuid;

-- Allow updates on tables that publish without a replica identity/PK
alter table public.config replica identity full;

update public.config
set id = gen_random_uuid()
where id is null;

alter table public.config alter column id set not null;

do $$
begin
  if not exists (
    select 1
    from pg_constraint
    where conrelid = 'public.config'::regclass
      and contype = 'p'
  ) then
    alter table public.config add primary key (id);
  end if;
end $$;

-- Ensure future inserts get an id automatically
alter table public.config alter column id set default gen_random_uuid();

-- Restore default replica identity now that PK exists
alter table public.config replica identity default;

