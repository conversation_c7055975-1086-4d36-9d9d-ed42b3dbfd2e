-- Ensure UUID generator is available
create extension if not exists pgcrypto;

-- Add a primary key to public.config so Zero can sync it
alter table public.config add column if not exists id uuid;

update public.config
set id = coalesce(id, gen_random_uuid());

alter table public.config alter column id set not null;

do $$
begin
  if not exists (
    select 1
    from pg_constraint
    where conrelid = 'public.config'::regclass
      and contype = 'p'
  ) then
    alter table public.config add primary key (id);
  end if;
end $$;


